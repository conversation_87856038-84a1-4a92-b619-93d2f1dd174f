{"name": "taostats", "version": "0.132.3", "private": true, "scripts": {"dev": "NODE_OPTIONS='--max-old-space-size=4096' next dev", "dev:3001": "NODE_OPTIONS='--max-old-space-size=4096' next dev -p 3001", "build": "NODE_OPTIONS='--max-old-space-size=4096' next build", "start": "NODE_OPTIONS='--max-old-space-size=4096' next start", "lint": "NODE_OPTIONS='--max-old-space-size=4096' next lint", "type-check": "NODE_OPTIONS='--max-old-space-size=4096' tsc --noEmit", "test:e2e": "NODE_OPTIONS='--max-old-space-size=4096' playwright test", "check": "NODE_OPTIONS='--max-old-space-size=4096' bun lint && bun type-check", "copy-files": "NODE_OPTIONS='--max-old-space-size=4096' cp -R node_modules/taostats-charting-library/charting_library/bundles public/charting_library"}, "dependencies": {"@ai-sdk/openai": "^1.3.22", "@ai-sdk/react": "^1.2.12", "@amplitude/analytics-browser": "^2.17.8", "@faker-js/faker": "^8.4.1", "@headlessui/react": "^2.2.0", "@next/eslint-plugin-next": "^14.2.13", "@polkadot/api": "^15.5.2", "@polkadot/extension-dapp": "^0.58.3", "@polkadot/types": "^15.5.2", "@polkadot/ui-keyring": "^3.12.1", "@polkadot/util-crypto": "^13.3.1", "@radix-ui/colors": "^3.0.0", "@radix-ui/react-accordion": "^1.1.2", "@radix-ui/react-dialog": "^1.1.4", "@radix-ui/react-icons": "^1.3.0", "@radix-ui/react-label": "^2.1.0", "@radix-ui/react-menubar": "^1.1.15", "@radix-ui/react-navigation-menu": "^1.1.4", "@radix-ui/react-popover": "^1.1.4", "@radix-ui/react-progress": "^1.1.7", "@radix-ui/react-radio-group": "^1.2.0", "@radix-ui/react-select": "^2.0.0", "@radix-ui/react-separator": "^1.0.3", "@radix-ui/react-slider": "^1.3.4", "@radix-ui/react-slot": "^1.1.0", "@radix-ui/react-switch": "^1.1.0", "@radix-ui/react-tabs": "^1.0.4", "@radix-ui/react-tooltip": "^1.0.7", "@react-hooks-library/core": "^0.6.2", "@react-spring/web": "^9.7.5", "@sentry/nextjs": "^7.119.2", "@sentry/react": "^7.119.2", "@t3-oss/env-nextjs": "^0.9.2", "@tanstack/match-sorter-utils": "^8.11.8", "@tanstack/query-core": "^5.60.6", "@tanstack/react-query": "^5.61.0", "@tanstack/react-query-devtools": "^5.61.4", "@tanstack/react-table": "^8.16.0", "@types/js-cookie": "^3.0.6", "@types/react-syntax-highlighter": "^15.5.13", "@typescript-eslint/eslint-plugin": "^8.8.0", "@typescript-eslint/parser": "^8.8.0", "@visx/axis": "^2.4.0", "@visx/brush": "^3.10.4", "@visx/curve": "^3.3.0", "@visx/event": "^2.1.2", "@visx/glyph": "^2.1.2", "@visx/gradient": "^2.1.0", "@visx/grid": "^2.4.0", "@visx/group": "^2.1.0", "@visx/heatmap": "^3.3.0", "@visx/hierarchy": "^3.3.0", "@visx/mock-data": "^3.3.0", "@visx/pattern": "^3.3.0", "@visx/responsive": "^3.10.2", "@visx/scale": "^2.2.2", "@visx/shape": "^2.4.0", "@visx/tooltip": "^2.2.2", "@visx/xychart": "^3.10.2", "@visx/zoom": "^3.3.0", "ai": "^4.3.16", "axios": "^1.7.2", "bignumber.js": "^9.1.2", "class-variance-authority": "^0.7.0", "clipboard-copy": "^4.0.1", "clsx": "^2.1.1", "cmdk": "^1.0.4", "d3": "^7.9.0", "date-fns": "^4.1.0", "emoji-regex": "^10.4.0", "ethers": "^6.13.5", "framer-motion": "^11.0.8", "generate-avatar": "^1.4.10", "jotai": "^2.10.3", "jotai-tanstack-query": "^0.9.0", "js-cookie": "^3.0.5", "lucide-react": "0.427.0", "moment": "^2.30.1", "moment-timezone": "^0.5.45", "next": "latest", "next-auth": "5.0.0-beta.25", "next-themes": "^0.3.0", "nextjs-toploader": "^1.6.11", "numerable": "^0.3.15", "parse-favicon": "^7.0.1", "plotly.js": "^3.0.1", "plotly.js-dist-min": "^3.0.1", "react": "^18", "react-countup": "^6.5.3", "react-day-picker": "8.10.1", "react-dom": "^18", "react-icons": "^5.0.1", "react-markdown": "^10.1.0", "react-plotly.js": "^2.6.0", "react-syntax-highlighter": "^15.5.0", "react-top-loading-bar": "^2.3.1", "remark-breaks": "^4.0.0", "remark-gfm": "^4.0.1", "semver": "^7.6.3", "sonner": "^1.4.41", "tailwind-merge": "^2.3.0", "tailwindcss-animate": "^1.0.7", "taostats": ".", "taostats-charting-library": "./deps/charting_library-28.5.0.tgz", "use-timer": "^2.0.1", "vaul": "^1.1.2", "zod": "^3.25.49"}, "devDependencies": {"@biomejs/biome": "1.6.3", "@playwright/test": "^1.44.0", "@types/d3": "^7.4.3", "@types/node": "^20", "@types/plotly.js": "^3.0.0", "@types/react": "^18", "@types/react-dom": "^18", "@types/react-plotly.js": "^2.6.3", "autoprefixer": "^10.0.1", "eslint": "^8", "eslint-config-next": "14.1.0", "eslint-plugin-react-hooks": "^4.6.2", "eslint-plugin-unused-imports": "^4.1.4", "git-cliff": "^2.5.0", "postcss": "^8", "tailwindcss": "^3.3.0", "typescript": "^5"}}