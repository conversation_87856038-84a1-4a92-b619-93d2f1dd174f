# Changelog

All notable changes to this project will be documented in this file.

## [unreleased]

### 🐛 Bug Fixes

- Subnet price

## [0.132.2] - 2025-06-12

### 🐛 Bug Fixes

- Add z-index to FAB
- Fix #2449: stake column

### 🚜 Refactor

- Remove-memoized currentUrl

### Update

- ChatFAB postion and opacity

## [0.132.1] - 2025-06-12

### 🐛 Bug Fixes

- Update current url based on navigation in SPA
- Remove additional opacity styles
- Fix #2435: update the docs link in header dropdown

### 🚜 Refactor

- To have one hook instance with init msgs

### Update

- Show Mira

## [0.132.0] - 2025-06-11

### 🚀 Features

- Add coloured code syntax
- Add Mira
- Feat #2433: put price in tab

### 🐛 Bug Fixes

- Avoid creating multiple versions based on msgid
- Window height in mobile
- Line height for formulas
- Bugs

### Update

- Suggested questions list
- Suggested questions

## [0.131.1] - 2025-06-10

### 🚀 Features

- Feat #2391: minor errors

## [0.131.0] - 2025-06-10

### 🚀 Features

- Enable saving chart
- Add Disclaimer drawer
- Add suggested questions component
- Feat #2391: add liquid hyperparameters

### 🐛 Bug Fixes

- Spacing and wordings
- Ui styling

### Update

- Link colors to brand color
- Links to be in teal and semi bold
- MaxSteps to 10

## [0.130.14] - 2025-06-09

### 🚀 Features

- Add features query param
- Enable saving chart

### 🐛 Bug Fixes

- Scroll issue and improve transition
- Calculate dynamic height
- Nav investors-menu state
- Nav analytics menu state
- Nav Blockchain menu state
- Validator and subnet list nav state
- Graph's responsiveness for mobile devices
- Avoid <= 0 input values for yield calculation
- Tags inlined with md text
- Jumpy navigation menu
- Mira formatting
- Lint error
- Sort Parent stake weight in desc. order

### 🚜 Refactor

- Refactor and fix: table and chart rendering

### Update

- Render all MD responses

## [0.130.13] - 2025-06-05

### 🐛 Bug Fixes

- Minor error

## [0.130.12] - 2025-06-05

### 🐛 Bug Fixes

- Minor errors

## [0.130.11] - 2025-06-05

### 🐛 Bug Fixes

- Nav subnet menu height and hide the menu onclick
- Fix #2360: update rewards tab

## [0.130.10] - 2025-06-05

### 🐛 Bug Fixes

- Fix #2360: rewards tab

## [0.130.9] - 2025-06-05

### 🐛 Bug Fixes

- Minor errors
- Stake history chart

## [0.130.8] - 2025-06-04

### 🚀 Features

- Add sessionId to query params
- Show skeleton while processing Markdown content
- Add custom table

### Update

- Ui changes
- Text-area styles
- From the old delegation page to staking tx

## [0.130.7] - 2025-06-03

### 🐛 Bug Fixes

- Validator menu

## [0.130.6] - 2025-06-02

### 🚀 Features

- Add reload response, send feedback and show graph
- Move fetch calls to actions

### 🐛 Bug Fixes

- Validator logo
- Update env variable
- Add chart settings to workflow files
- Hide subnet menu when navigated
- Alpha proportion

### Update

- Name and add BETA flag

## [0.130.5] - 2025-06-02

### 🐛 Bug Fixes

- Validator logo

## [0.130.4] - 2025-06-02

### 🐛 Bug Fixes

- Fix #2313: alpha proportion

## [0.130.3] - 2025-05-31

### 🐛 Bug Fixes

- Wallet format

## [0.130.2] - 2025-05-31

### 🐛 Bug Fixes

- Fix #2272: split volume in subnets chart

## [0.130.1] - 2025-05-31

### 🐛 Bug Fixes

- Config all over the place following merge
- Fix #2310: subnet emission description

### 🚜 Refactor

- Change chart settings to be compatable with new env layout

## [0.130.0] - 2025-05-30

### 🚀 Features

- Feat #2307: update middle info box of subnets page

## [0.129.11] - 2025-05-30

### 🐛 Bug Fixes

- Fix #2295: update alpha symbol

## [0.129.10] - 2025-05-29

### 🚀 Features

- Save load charts for individul subnets
- Scope by subnet
- Add chart settings endpoint to config
- Add subnet's new hyperparams

### 🐛 Bug Fixes

- Remove hard code env
- Fix build
- Wallet format

### Update

- Epochs and commit_weights order

## [0.129.8] - 2025-05-28

### 🐛 Bug Fixes

- Wallet format in extrinsic page

## [0.129.7] - 2025-05-28

### 🐛 Bug Fixes

- Alpha symbol in metagrahp table

## [0.129.6] - 2025-05-27

### 🐛 Bug Fixes

- Fix #2272: update the tooltip color

## [0.129.5] - 2025-05-27

### 🐛 Bug Fixes

- Wallet address format in single account page

## [0.129.4] - 2025-05-27

### 🐛 Bug Fixes

- Fix #2272: minor error

## [0.129.3] - 2025-05-27

### 🐛 Bug Fixes

- Minor errors
- Fix #2272: swap order of the bar & tooltip color

## [0.129.2] - 2025-05-27

### 🐛 Bug Fixes

- Wallet format in account page

## [0.129.1] - 2025-05-27

### 🐛 Bug Fixes

- Table pagination

## [0.129.0] - 2025-05-27

### 🚀 Features

- Mock agent typing simulation and timestamp label
- Add styled chat FAB
- Display response from MCP server
- MCP integration uncleaned
- Render UI elements based on tags
- Add env for mcp
- Feat #2272: Split Volume in Subnets Chart

### 🐛 Bug Fixes

- Hydration error
- Build TS error
- Coldkey distribution format
- Single validator header
- Hotkey format
- Child hotkey
- Update env name and url

### Update

- Styling and add Suggested questions

## [0.128.13] - 2025-05-26

### 🚀 Features

- Update endpoint and smooth chart data processing

### 🐛 Bug Fixes

- Github actions file
- Single validator staked chart style
- Single validator reward chart style
- Single validator weights chart style

## [0.128.12] - 2025-05-22

### 🐛 Bug Fixes

- Fix #2239: chart cut off

## [0.128.11] - 2025-05-22

### 🐛 Bug Fixes

- Fix #2240: validator format

## [0.128.10] - 2025-05-22

### 🐛 Bug Fixes

- Fix #2240: cover by icon

## [0.128.9] - 2025-05-22

### 🚀 Features

- Set chart default to linear and then switcheroo prices to log

### 🐛 Bug Fixes

- Validator format

## [0.128.8] - 2025-05-22

### 🚀 Features

- Set chart default to linear and then switcheroo prices to log

## [0.128.7] - 2025-05-22

### 🐛 Bug Fixes

- Fix #2240; tooltip error

## [0.128.6] - 2025-05-22

### 🐛 Bug Fixes

- Fix #2240: minor error

## [0.128.5] - 2025-05-22

### 🚀 Features

- Animated incentive distribution chart 24h

### 🐛 Bug Fixes

- Remove unused file
- Fix #2240: wallet format
- Fix #2240: registration table wallet format

### 🚜 Refactor

- Refactoring to a static y-axis

## [0.128.4] - 2025-05-21

### 🐛 Bug Fixes

- Subnet menu resize

## [0.128.3] - 2025-05-21

### 🐛 Bug Fixes

- Fix #2229: error message
- Scroll blocking

## [0.128.2] - 2025-05-21

### 🐛 Bug Fixes

- Fix #2229: show error message

## [0.128.1] - 2025-05-20

### 🚀 Features

- Feat #2053: minor errors

## [0.128.0] - 2025-05-20

### 🚀 Features

- Feat #2053: tao staked to root over time chart

## [0.127.0] - 2025-05-20

### 🚀 Features

- Feat #2053: tao staked to root

## [0.126.4] - 2025-05-20

### 🐛 Bug Fixes

- Remove CSV

## [0.126.3] - 2025-05-20

### 🐛 Bug Fixes

- Fix #2197: add time control

## [0.126.2] - 2025-05-20

### 🐛 Bug Fixes

- Minor error

## [0.126.1] - 2025-05-20

### 🐛 Bug Fixes

- Disable alpha staking chart

## [0.126.0] - 2025-05-20

### 🚀 Features

- Feat #2146: add validator name
- Feat #2059: update hyperparameters

## [0.124.1] - 2025-05-19

### 🚀 Features

- Feat #2056: add documentation link

## [0.124.0] - 2025-05-19

### 🚀 Features

- Feat #2056: chart rendering

## [0.123.1] - 2025-05-19

### 🚀 Features

- Feat #2103: change action with type

## [0.123.0] - 2025-05-19

### 🚀 Features

- Feat #2103: add action selector

## [0.122.0] - 2025-05-19

### 🚀 Features

- Feat #2056: tao staked in alpha over time chart

## [0.121.4] - 2025-05-15

### 🐛 Bug Fixes

- Add link

## [0.121.3] - 2025-05-15

### 🐛 Bug Fixes

- Chart overlap

## [0.121.2] - 2025-05-15

### 🐛 Bug Fixes

- Total Subnets Price on main subnets page
- Move y-axis to the right

## [0.121.1] - 2025-05-15

### 🐛 Bug Fixes

- Add switch

## [0.121.0] - 2025-05-14

### 🚀 Features

- Feat #2141: update validator menu

## [0.120.7] - 2025-05-14

### 🐛 Bug Fixes

- Remove link

## [0.120.6] - 2025-05-14

### 🐛 Bug Fixes

- Subnet price chart

## [0.120.5] - 2025-05-14

### 🐛 Bug Fixes

- F&G card
- Minor error

## [0.120.4] - 2025-05-14

### 🐛 Bug Fixes

- Subnet value on subnets menu

## [0.120.3] - 2025-05-14

### 🐛 Bug Fixes

- Tooltip date

## [0.120.2] - 2025-05-14

### 🐛 Bug Fixes

- Lint error

## [0.120.1] - 2025-05-14

### 🐛 Bug Fixes

- Volume chart
- Show unit in volume tooltip

## [0.120.0] - 2025-05-13

### 🚀 Features

- Feat #2142: add subnets total price to subnets menu

## [0.119.3] - 2025-05-13

### 🐛 Bug Fixes

- Main search bar

## [0.119.2] - 2025-05-13

### 🐛 Bug Fixes

- Fix #2064: Y axis label

## [0.119.1] - 2025-05-13

### 🐛 Bug Fixes

- Fix #2064: subnet price chart tooltip

## [0.119.0] - 2025-05-13

### 🚀 Features

- Feat #2064: subnets price chart

## [0.118.1] - 2025-05-12

### 🐛 Bug Fixes

- Subnets logo

## [0.118.0] - 2025-05-12

### 🚀 Features

- Feat #2064: add volume to subnets price chart

## [0.117.3] - 2025-05-12

### 🐛 Bug Fixes

- Fix #2058: tooltip

## [0.117.2] - 2025-05-12

### 🐛 Bug Fixes

- Fix #2058: minor errors

## [0.117.1] - 2025-05-12

### 🐛 Bug Fixes

- Fix #2058: Y axios

## [0.117.0] - 2025-05-12

### 🚀 Features

- Feat #2058: Root Proportion chart

## [0.116.1] - 2025-05-12

### 🐛 Bug Fixes

- Symbol- yield return and responsiveness chart
- Subnets menu search input

## [0.116.0] - 2025-05-09

### 🚀 Features

- Feat #2041: subnets table description
- Feat #2041: tooltip description into single file

## [0.115.15] - 2025-05-09

### 🐛 Bug Fixes

- Yield base url

## [0.115.14] - 2025-05-09

### 🐛 Bug Fixes

- Single validator registered to link

## [0.115.13] - 2025-05-09

### 🐛 Bug Fixes

- Fix subnet page tooltips working on mobile
- Validator link

## [0.115.12] - 2025-05-08

### 🐛 Bug Fixes

- Fix subnet page tooltips working on mobile

## [0.115.11] - 2025-05-08

### 🐛 Bug Fixes

- Menu status on subnet menu

## [0.115.10] - 2025-05-08

### 🐛 Bug Fixes

- Bubble table

## [0.115.9] - 2025-05-08

### 🐛 Bug Fixes

- Fix #2079: account address link

## [0.115.8] - 2025-05-08

### 🚀 Features

- Show yield returns

### 🐛 Bug Fixes

- Subnet menu status error

## [0.115.7] - 2025-05-08

### 🐛 Bug Fixes

- Set cookie error

## [0.115.6] - 2025-05-08

### 🐛 Bug Fixes

- Lint error

## [0.115.5] - 2025-05-08

### 🐛 Bug Fixes

- Adding cookie values

## [0.115.4] - 2025-05-08

### 🐛 Bug Fixes

- Subnets menu

## [0.115.3] - 2025-05-08

### 🐛 Bug Fixes

- Fix #2040: metegraph table
- #2040: validator table

## [0.115.2] - 2025-05-07

### 🐛 Bug Fixes

- Validator name issue on yield page
- Validator name issue on validators page
- Validator name issue on homepage

## [0.115.1] - 2025-05-07

### 🐛 Bug Fixes

- Subnets menu if there is no subnet

## [0.115.0] - 2025-05-07

### 🚀 Features

- Feat #2063: add All subnet pagination

## [0.114.19] - 2025-05-07

### 🐛 Bug Fixes

- Change title with sentiment
- Update total price api

## [0.114.17] - 2025-05-06

### 🐛 Bug Fixes

- Minor error
- Hide F & G chart

## [0.114.16] - 2025-05-06

### 🐛 Bug Fixes

- Skeleton
- Add F & G chart on subnets table

## [0.114.15] - 2025-05-06

### 🐛 Bug Fixes

- Lint error

## [0.114.14] - 2025-05-06

### 🐛 Bug Fixes

- Add F & G card on single subnet page
- Add F & G card on main subnets page
- Subnets table header section

## [0.114.13] - 2025-05-06

### 🐛 Bug Fixes

- Change Subnets Home to Subnets

## [0.114.12] - 2025-05-06

### 🐛 Bug Fixes

- Set default sort field on main subnets page
- Set MC default sort on subnets menu

## [0.114.11] - 2025-05-06

### 🐛 Bug Fixes

- Add root prop sort

## [0.114.10] - 2025-05-06

### 🐛 Bug Fixes

- Update root prop on main subnets page
- Update root prop on single subnet page

## [0.114.9] - 2025-05-05

### 🐛 Bug Fixes

- Subnet grid method
- Search in grid method
- Subnet list method

## [0.114.8] - 2025-05-05

### 🐛 Bug Fixes

- Symbol in subnet data section

## [0.114.7] - 2025-05-05

### 🐛 Bug Fixes

- Update miner weights chart api endpoint

## [0.114.6] - 2025-05-05

### 🐛 Bug Fixes

- Fix #1748: Subnet Data Section

## [0.114.5] - 2025-05-05

### 🐛 Bug Fixes

- Fix #1748: change miners tab to statistics tab

## [0.114.4] - 2025-05-02

### 🐛 Bug Fixes

- Add root_proportion on main subnets table

## [0.114.3] - 2025-05-02

### 🐛 Bug Fixes

- Root proportion
- Fix #1748: set ALL default in price chart

## [0.114.2] - 2025-05-02

### 🐛 Bug Fixes

- Fix #1748: immune chart tooltip
- Fix #1748: minor error

## [0.114.1] - 2025-05-02

### 🐛 Bug Fixes

- Fix #1748: immune chart tooltip

## [0.114.0] - 2025-05-02

### 🚀 Features

- Feat #1988: add root proportion card

## [0.113.6] - 2025-05-02

### 🐛 Bug Fixes

- Fix #1748: line overlap
- Fix #1748: tooltip of price chart

## [0.113.5] - 2025-05-02

### 🚀 Features

- Feat #1748: add range dropdown immune chart

### 🐛 Bug Fixes

- Fix #1748: remove volume axis

## [0.113.4] - 2025-05-01

### 🐛 Bug Fixes

- Fix #1748: all selector on emission chart
- Fix #1748: add T label after price
- Fix #1748: remove the zoom in the price chart

## [0.113.3] - 2025-05-01

### 🐛 Bug Fixes

- Single subnet symbol

## [0.113.1] - 2025-05-01

### 🚀 Features

- Feat #1748: minor error
- Feat #1748: enable miners tab
- Feat #1748: subnet price chart

## [0.113.0] - 2025-04-30

### 🚀 Features

- Feat #1748: Immune miner chart

## [0.112.1] - 2025-04-30

### 🚀 Features

- Feat #1748: price chart 3 digits of price
- Feat #1748: remove top 10 holder table
- Feat #1748: immune miner chart
- Feat #1748: change statistics to miners
- Feat #1748: update emission chart

### 🐛 Bug Fixes

- Subnets menu

## [0.111.1] - 2025-04-30

### 🚀 Features

- Feat #1748: subnet price chart on statistics tab
- Feat #1748: disable statistics tab

### 🐛 Bug Fixes

- Subnets menu

## [0.111.0] - 2025-04-29

### 🚀 Features

- Feat #1748: update emission chart
- Feat #1748: set default order in single holders table
- Feat #1748: add top 10 holders table on statistics tab

## [0.110.91] - 2025-04-28

### 🚀 Features

- Feat #1748: add emission chart on statistics tab

### 🐛 Bug Fixes

- Mouse hover action

## [0.110.90] - 2025-04-28

### 🐛 Bug Fixes

- Hover action
- Mouse hover action

## [0.110.89] - 2025-04-28

### 🐛 Bug Fixes

- Subnets menu
- Minor error
- Default select

## [0.110.88] - 2025-04-28

### 🐛 Bug Fixes

- Subnets menu

## [0.110.87] - 2025-04-28

### 🐛 Bug Fixes

- Footer style
- Subnets menu

## [0.110.86] - 2025-04-28

### 🐛 Bug Fixes

- Footer style

## [0.110.85] - 2025-04-25

### 🐛 Bug Fixes

- Subnet 23 logo

## [0.110.84] - 2025-04-25

### 🐛 Bug Fixes

- Balance chart

## [0.110.83] - 2025-04-25

### 🐛 Bug Fixes

- Account chart style
- Delegation chart

## [0.110.82] - 2025-04-24

### 🐛 Bug Fixes

- Tokenomics chart
- Subnet registration chart

## [0.110.81] - 2025-04-23

### 🐛 Bug Fixes

- Add 1M column on main subnets page

## [0.110.80] - 2025-04-23

### 🐛 Bug Fixes

- Subnet 14 logo
- Drop staking page

## [0.110.79] - 2025-04-23

### 🐛 Bug Fixes

- Subnet 14 logo

## [0.110.78] - 2025-04-22

### 🐛 Bug Fixes

- Validator link on balances table
- Performance chart
- Account delegate chart

## [0.110.77] - 2025-04-22

### 🐛 Bug Fixes

- Search modal
- Fix symbol mouseover on single validator page

## [0.110.76] - 2025-04-21

### 🐛 Bug Fixes

- Symbol on total subnets price chart
- Subnet 49 logo
- Social discord link

## [0.110.75] - 2025-04-17

### 🐛 Bug Fixes

- Lint error
- Stake balance history chart
- Performance chart

## [0.110.74] - 2025-04-17

### 🐛 Bug Fixes

- Remove deviation page

## [0.110.73] - 2025-04-16

### 🐛 Bug Fixes

- Blockchain table
- Update sportstensor logo
- Single validator table ordering

## [0.110.72] - 2025-04-16

### 🐛 Bug Fixes

- Latest registration cost chart
- Deregistration chart
- Update blocks table

## [0.110.71] - 2025-04-15

### 🐛 Bug Fixes

- Dashboard menu order

## [0.110.70] - 2025-04-15

### 🐛 Bug Fixes

- Remove default description

## [0.110.69] - 2025-04-15

### 🐛 Bug Fixes

- Update subnets logo
- Subnets logo error

## [0.110.68] - 2025-04-15

### 🐛 Bug Fixes

- Update subnets logo

## [0.110.67] - 2025-04-15

### 🐛 Bug Fixes

- Disable the twitter logo

## [0.110.66] - 2025-04-14

### 🐛 Bug Fixes

- Remove emission tab in single subnet page

## [0.110.65] - 2025-04-14

### 🐛 Bug Fixes

- Minor error
- Replace delegation/staking with yield

## [0.110.64] - 2025-04-14

### 🐛 Bug Fixes

- Validator menu on mobile
- Lint error
- Daily registration recycle chart
- Top 50 account balances
- Recent largest transactions chart
- Realtime transaction volume chart
- New account balance chart
- Recent exchange transaction chart

## [0.110.63] - 2025-04-14

### 🐛 Bug Fixes

- Validator header menu
- Lint error
- Validator menu on mobile

## [0.110.62] - 2025-04-14

### 🐛 Bug Fixes

- Header column
- Validator header menu

## [0.110.61] - 2025-04-12

### 🐛 Bug Fixes

- Subnets total price chart pagination
- Order by Tao col
- Alpha blank

## [0.110.60] - 2025-04-11

### 🐛 Bug Fixes

- Minor error
- Subnets total price chart pagination

## [0.110.59] - 2025-04-11

### 🐛 Bug Fixes

- Subnets total price chart

## [0.110.58] - 2025-04-11

### 🐛 Bug Fixes

- Subnets total price chart
- Amount of Staked Tao chart
- Validator table column
- Staking vs price chart
- Daily chart
- Chain block chart
- Subnet growth chart
- Weekly extrinsic chart

## [0.110.57] - 2025-04-11

### 🐛 Bug Fixes

- Subnet marker mobile responsive

## [0.110.56] - 2025-04-11

### 🐛 Bug Fixes

- Tooltip width

## [0.110.55] - 2025-04-11

### 🐛 Bug Fixes

- Tooltip description
- Css style error

## [0.110.54] - 2025-04-11

### 🐛 Bug Fixes

- Dominance ordering
- Subnet marker

## [0.110.53] - 2025-04-11

### 🐛 Bug Fixes

- Validator link on metagraph table

## [0.110.52] - 2025-04-11

### 🐛 Bug Fixes

- Subnet marker

## [0.110.51] - 2025-04-11

### 🐛 Bug Fixes

- Transaction table filter

## [0.110.50] - 2025-04-10

### 🐛 Bug Fixes

- Minor error

## [0.110.49] - 2025-04-10

### 🐛 Bug Fixes

- Transaction table column

## [0.110.48] - 2025-04-10

### 🐛 Bug Fixes

- Add marker on subnet
- Add amount filters to transaction table

## [0.110.46] - 2025-04-10

### 🐛 Bug Fixes

- Link validator page
- Minor error
- Total subnets price chart

## [0.110.45] - 2025-04-10

### 🐛 Bug Fixes

- Link validator page

## [0.110.44] - 2025-04-10

### 🐛 Bug Fixes

- Meta image
- Default order select

## [0.110.43] - 2025-04-10

### 🐛 Bug Fixes

- Meta image

## [0.110.42] - 2025-04-09

### 🐛 Bug Fixes

- Investors header menu width

## [0.110.41] - 2025-04-09

### 🚀 Features

- Tweak nav

### 🐛 Bug Fixes

- Registration cost chart style
- Top emission chart style
- Number of Transfers chart style
- Number of Accounts chart style

## [0.110.40] - 2025-04-09

### 🐛 Bug Fixes

- Zoom button

## [0.110.39] - 2025-04-09

### 🐛 Bug Fixes

- Meta image on yield page
- Extrinsics table column
- Total subnets total price chart

## [0.110.37] - 2025-04-09

### 🐛 Bug Fixes

- Update meta data on yield page

## [0.110.36] - 2025-04-08

### 🐛 Bug Fixes

- Table header sticky

## [0.110.35] - 2025-04-08

### 🐛 Bug Fixes

- Remove chart label
- Remove pagination

## [0.110.34] - 2025-04-08

### 🐛 Bug Fixes

- Validator menu

## [0.110.33] - 2025-04-08

### 🐛 Bug Fixes

- Set subnet link

## [0.110.32] - 2025-04-08

### 🐛 Bug Fixes

- Column align
- Stake column

## [0.110.31] - 2025-04-08

### 🐛 Bug Fixes

- Order by 1D
- Remove subnet column & dropdown shows the subnet
- Data resets back to root

## [0.110.30] - 2025-04-08

### 🚀 Features

- Update yield chart

### 🐛 Bug Fixes

- Remove green line
- Stake symbol
- Add validator column
- Yield table select
- Chart date

## [0.110.28] - 2025-04-07

### 🚀 Features

- Yield chart
- Yield table
- Yield graph tab
- Yield header skeleton
- Yield table subnet select

### 🐛 Bug Fixes

- Name field in yield table

## [0.110.27] - 2025-04-07

### 🐛 Bug Fixes

- Prevent shoft f triggering in input fields

## [0.110.26] - 2025-04-07

### 🚀 Features

- Button CTA for data
- Adjust address padding on single account page mobile view

### 🚜 Refactor

- Remove unused prop

## [0.110.25] - 2025-04-04

### 🐛 Bug Fixes

- Subnet table header sticky on home page
- Validator table header sticky on home page
- Transaction table header sticky
- Extrinsics table header sticky

## [0.110.24] - 2025-04-03

### 🚀 Features

- Change alpha to red

### 🐛 Bug Fixes

- Do not share atom so charts are unique
- Key error
- P nesting console error
- Nested P hydration console error
- Nested P console error
- Remove block/daily/v1 api
- Remove hotkey/family/history/v1
- Remove subnet/registration-cost/latest/v1

### 🚜 Refactor

- Scope total to single hotkey

## [0.110.23] - 2025-04-02

### 🐛 Bug Fixes

- Validator onchain id

## [0.110.22] - 2025-04-02

### 🐛 Bug Fixes

- Miner emissions heatmap chart

## [0.110.21] - 2025-04-02

### 🐛 Bug Fixes

- Emission chart tooltip

## [0.110.20] - 2025-04-02

### 🐛 Bug Fixes

- Number Transfer Chart color
- Number Account Chart color

## [0.110.19] - 2025-04-01

## [0.110.18] - 2025-04-01

### 🐛 Bug Fixes

- Csv download

## [0.110.17] - 2025-04-01

### 🐛 Bug Fixes

- Chart tooltip position

## [0.110.16] - 2025-04-01

### 🚀 Features

- Remove 24hr change columns and balance history
- Balance chart driven by table row click and scoped to coldkey/hotkey/netuid
- Move table to chart row

### 🐛 Bug Fixes

- Validator table
- Chart layout colspan
- Miner Ip Distribution Chart

### 🚜 Refactor

- Prep for scoping chart
- Layout tweaks

## [0.110.15] - 2025-03-31

### 🐛 Bug Fixes

- Format error
- Proportion

## [0.110.14] - 2025-03-31

### 🐛 Bug Fixes

- Mobile dash link fix
- Format error

## [0.110.13] - 2025-03-31

### 🐛 Bug Fixes

- Validator page

## [0.110.12] - 2025-03-31

### 🐛 Bug Fixes

- Subnet total
- Subnet total price

## [0.110.11] - 2025-03-31

### 🐛 Bug Fixes

- Subnet total

## [0.110.10] - 2025-03-31

### 🐛 Bug Fixes

- Mob nav
- Single subnet left section

## [0.110.9] - 2025-03-28

## [0.110.7] - 2025-03-27

### 🐛 Bug Fixes

- Nominator table

## [0.110.8] - 2025-03-28

### 🐛 Bug Fixes

- Beta using dev image
- Mob nav

## [0.110.6] - 2025-03-27

### 🐛 Bug Fixes

- X site auth cookie

## [0.110.5] - 2025-03-27

### 🐛 Bug Fixes

- Miner coldkey distribution chart

## [0.110.4] - 2025-03-27

### 🚀 Features

- Add additional links to dash popover
- Tweak styling
- Search tooltip
- Add profile dropdown
- Dash popover on mouse in out

### 🐛 Bug Fixes

- Button console error
- Price formatter nested <p> console error
- Use config for dash link in popover
- Make dash url public
- Include dash path in docker build
- Miner coldkey distribution chart

### 🚜 Refactor

- Opacity dependent on open/close
- Chevron to match
- Remove unused imports

## [0.110.3] - 2025-03-27

### 🚀 Features

- Feat #665: add view documentation link

## [0.110.2] - 2025-03-26

### 🚀 Features

- Feat #665: cumulative subnets chart

## [0.110.1] - 2025-03-26

### 🚀 Features

- Feat #665: update cumulative subnets chart

## [0.110.0] - 2025-03-26

### 🚀 Features

- Feat #665: cumulative subnets chart

## [0.109.11] - 2025-03-26

### 🐛 Bug Fixes

- Remove tatsu validator 
- Yuma validator logo

## [0.109.10] - 2025-03-25

### 🐛 Bug Fixes

- Transaction table in single account page
- Underline color
- Remove tatsu validator

## [0.109.9] - 2025-03-24

### 🐛 Bug Fixes

- Update Crucible Labs logo
- Yuma logo

## [0.109.8] - 2025-03-24

### 🐛 Bug Fixes

- Validator logo
- Update Crucible Labs logo

## [0.109.7] - 2025-03-24

### 🐛 Bug Fixes

- Validator logo

## [0.109.6] - 2025-03-24

### 🐛 Bug Fixes

- Update website icon
- Validator onchain id
- Update validator logo

## [0.109.5] - 2025-03-24

### 🐛 Bug Fixes

- Alpha in pool dollar value

## [0.109.4] - 2025-03-22

### 🚀 Features

- Feat #1611: add docs link

### 🐛 Bug Fixes

- Validator meta title name
- Minor errors

## [0.109.3] - 2025-03-21

### 🚀 Features

- Feat #1611: fix validator alpha heatmap chart color
- Feat #1611: add link

### 🐛 Bug Fixes

- Action type in transaction table
- Validator onchain id

## [0.109.1] - 2025-03-20

### 🐛 Bug Fixes

- Deregistration chart

## [0.109.0] - 2025-03-20

### 🚀 Features

- Feat #1611: add api endpoint for validator alpha heatmaps chart
- Feat #1611: validator alpha heatmaps chart
- Feat #1611: validator alpha heatmap chart tooltip

### 🐛 Bug Fixes

- Validator alpha heatmap chart

## [0.108.22] - 2025-03-20

### 🐛 Bug Fixes

- Hotkey link

## [0.108.21] - 2025-03-19

### 🐛 Bug Fixes

- Search by key
- Remove emoji
- Proportion of child validator

## [0.108.20] - 2025-03-19

### 🐛 Bug Fixes

- Validator

## [0.108.19] - 2025-03-19

### 🚀 Features

- Add empty bar when no balance

### 🐛 Bug Fixes

- Handling zero on balance % calculation

### ⚙️ Miscellaneous Tasks

- Cap captcha landing logo to 240px

## [0.108.18] - 2025-03-18

### 🐛 Bug Fixes

- Validator performance chart

## [0.108.17] - 2025-03-18

### 🐛 Bug Fixes

- Single subnet meta title

## [0.108.16] - 2025-03-18

### 🐛 Bug Fixes

- Deregistration chart

## [0.108.15] - 2025-03-18

### 🐛 Bug Fixes

- Metagraph table
- Validator performance chart
- Validator table header sticky

## [0.108.14] - 2025-03-17

### 🐛 Bug Fixes

- Metagraph table

## [0.108.13] - 2025-03-17

### 🐛 Bug Fixes

- Add delegate link in single account page
- Delegate underline color

## [0.108.12] - 2025-03-17

### 🐛 Bug Fixes

- Metagraph table scroll
- Metagraph header stickly

## [0.108.10] - 2025-03-14

### 🐛 Bug Fixes

- Change value in red boxes
- Move tao/usd switcher
- Account page

## [0.108.9] - 2025-03-13

### 🐛 Bug Fixes

- Minor errors
- Empty column

## [0.108.8] - 2025-03-13

### 🐛 Bug Fixes

- Validator performance chart

## [0.108.7] - 2025-03-13

### 🐛 Bug Fixes

- Restore account history limit so it gets the last 2000 days, not the last 50

## [0.108.6] - 2025-03-13

### 🐛 Bug Fixes

- Search for key

## [0.108.5] - 2025-03-13

### 🐛 Bug Fixes

- Subnet icon

## [0.108.4] - 2025-03-13

### 🐛 Bug Fixes

- Disable performance chart

## [0.108.2] - 2025-03-13

### 🐛 Bug Fixes

- Only number allow

## [0.108.1] - 2025-03-13

### 🐛 Bug Fixes

- Validator performance chart

## [0.108.0] - 2025-03-13

### 🐛 Bug Fixes

- Validator performance chart

### ⚙️ Miscellaneous Tasks

- Add Cloudflare captcha custom landing page
- Set bg-color

## [0.107.74] - 2025-03-12

### 🐛 Bug Fixes

- Block table header

## [0.107.73] - 2025-03-12

### 🐛 Bug Fixes

- Minor errors

## [0.107.72] - 2025-03-11

### 🐛 Bug Fixes

- Header menu

## [0.107.71] - 2025-03-11

### 🐛 Bug Fixes

- Validator page

## [0.107.70] - 2025-03-11

### 🐛 Bug Fixes

- Single account transaction table
- Subnet table scroll

## [0.107.69] - 2025-03-11

### 🐛 Bug Fixes

- Single account transaction table

## [0.107.68] - 2025-03-11

### 🐛 Bug Fixes

- Hyperparams
- Single subnet onchain ID

## [0.107.67] - 2025-03-11

### 🐛 Bug Fixes

- Hyperparams

## [0.107.66] - 2025-03-10

### 🚜 Refactor

- Handle different scales on y axes

## [0.107.65] - 2025-03-10

## [0.107.64] - 2025-03-10

### 🐛 Bug Fixes

- Remove emojis

### 🚜 Refactor

- Front load all stake balance data to enable client side sorting and prevent refresh

## [0.107.63] - 2025-03-10

### 🐛 Bug Fixes

- Tao/usd switcher

## [0.107.62] - 2025-03-10

### 🐛 Bug Fixes

- Add search in holders table
- Left section
- Statistic for all subnets

## [0.107.61] - 2025-03-10

### 🐛 Bug Fixes

- Single validator description

## [0.107.60] - 2025-03-10

### 🐛 Bug Fixes

- Single validator page

### 🚜 Refactor

- Remove unused components

### ⚡ Performance

- Get account header data for initial load only
- Do not refetch stake balance history chart data
- Single load account

## [0.107.59] - 2025-03-07

### 🐛 Bug Fixes

- Header menu
- Tokenomics page

## [0.107.58] - 2025-03-07

## [0.107.57] - 2025-03-07

### 🚜 Refactor

- Stack delegated balance

## [0.107.56] - 2025-03-07

## [0.107.55] - 2025-03-07

## [0.107.54] - 2025-03-07

### 🐛 Bug Fixes

- Fix subnets chart page thrashing

## [0.107.53] - 2025-03-07

### 🐛 Bug Fixes

- Fix subnet chart page thrashing in dev after hot reload

## [0.107.52] - 2025-03-06

### 🐛 Bug Fixes

- Add selector

## [0.107.51] - 2025-03-06

### 🐛 Bug Fixes

- Remove description api & subnets json

### 🚜 Refactor

- Use account endpoint update for true 24h balances

## [0.107.48] - 2025-03-06

### 🚀 Features

- Feat #747: daily recycle chart
- Feat #747: update daily registration recycle chart

## [0.107.47] - 2025-03-05

### 🐛 Bug Fixes

- Updated block layout
- Subnet tooltip

## [0.107.46] - 2025-03-04

### 🐛 Bug Fixes

- Reg cost chart

## [0.107.45] - 2025-03-04

### 🐛 Bug Fixes

- Max y-axis
- Reg cost zoom chart

## [0.107.44] - 2025-03-04

### 🐛 Bug Fixes

- Disable zoom in/out

## [0.107.43] - 2025-03-04

### 🐛 Bug Fixes

- Reg cost chart

## [0.107.42] - 2025-03-04

### 🐛 Bug Fixes

- Reg cost chart

## [0.107.41] - 2025-03-04

### 🐛 Bug Fixes

- Transaction table

### 🚜 Refactor

- Moblify using media queries over js helper

### Feature

- Chart display total balance

## [0.107.40] - 2025-03-03

### 🐛 Bug Fixes

- Coldkey link in metagraph table

## [0.107.39] - 2025-03-03

### 🐛 Bug Fixes

- Metagraph table

## [0.107.38] - 2025-03-03

### 🐛 Bug Fixes

- Header menu

## [0.107.37] - 2025-03-03

### 🐛 Bug Fixes

- Validator link

### 🚜 Refactor

- Desktop header styles
- Adjust table layout to reduce flicker

## [0.107.36] - 2025-03-03

### 🐛 Bug Fixes

- Add Y axios
- Single subnets left section text align
- Icon size
- Wallet skeleton
- Minor errors

## [0.107.35] - 2025-02-28

### 🐛 Bug Fixes

- Reg cost chart

## [0.107.34] - 2025-02-28

### 🐛 Bug Fixes

- Reg cost chart

## [0.107.33] - 2025-02-28

### 🐛 Bug Fixes

- Stake chart
- Reg cost chart

## [0.107.32] - 2025-02-28

### 🐛 Bug Fixes

- Remove console
- Reg cost chart

### 🚜 Refactor

- Remove unused variant and public key props from wallet header
- Move out trend icon and account cards into own component files

## [0.107.31] - 2025-02-28

### 🐛 Bug Fixes

- Reg cost zoom chart

## [0.107.30] - 2025-02-28

### 🐛 Bug Fixes

- Line chart shouldn't be area chart

## [0.107.29] - 2025-02-28

### 🐛 Bug Fixes

- Nonsense lint error

### ⚙️ Miscellaneous Tasks

- Support manual trigger
- Move files back due to some pipeline build error (possibly cache?)
- More unecessary file restores for build cache errors
- Undo file restore

## [0.107.28] - 2025-02-28

### 🐛 Bug Fixes

- CK Take in single validator table

## [0.107.27] - 2025-02-28

### 🐛 Bug Fixes

- Ck Take
- CK Take in single validator table

## [0.107.26] - 2025-02-28

### 🐛 Bug Fixes

- Ck Take

## [0.107.25] - 2025-02-27

### 🐛 Bug Fixes

- Single validator table empty cell

## [0.107.24] - 2025-02-27

### 🐛 Bug Fixes

- Staked chart color
- Legacy weights link

## [0.107.23] - 2025-02-27

### 🐛 Bug Fixes

- CK Take

## [0.107.22] - 2025-02-27

### 🚀 Features

- Add TV Chart to subnets page

### 🐛 Bug Fixes

- Add top boxes
- Missing validator

### 🚜 Refactor

- Add title and subtitle to Subnets Total TV Chart

## [0.107.21] - 2025-02-27

### 🐛 Bug Fixes

- Add top boxes

### 🚜 Refactor

- Tweak total chart colours to match card
- Subnet balance history chart theming

## [0.107.20] - 2025-02-27

### 🐛 Bug Fixes

- Single subnet holders table
- Remove delegate from transactions on validator page
- Update Weights to Legacy Weights
- Update colours in Rewards
- Add change to top boxes

## [0.107.19] - 2025-02-27

### 🚀 Features

- 24h balance change

### 🐛 Bug Fixes

- Build error
- Single subnet holders table

## [0.107.18] - 2025-02-27

### 🐛 Bug Fixes

- Tooltip of reg cost chart

## [0.107.17] - 2025-02-27

### 🐛 Bug Fixes

- Single subnet transaction table
- Single subnet left section

## [0.107.16] - 2025-02-26

### 🐛 Bug Fixes

- Remove home

## [0.107.15] - 2025-02-26

### 🚀 Features

- Add percent of  staked
- Add balances subnet select dropdown

### 🐛 Bug Fixes

- Stake balance percent header name
- Tab naming
- Restore old EVM page
- Remove tensor exchange
- Update main menu

### 🚜 Refactor

- Move back to balances tab

## [0.107.12] - 2025-02-26

### 🐛 Bug Fixes

- Issue where validator list could not be scrolled within settings dialog in wallet section on subnet page
- Subnets table column align
- Minor errors

## [0.107.11] - 2025-02-26

### 🐛 Bug Fixes

- Buy page

### 🚜 Refactor

- Match spacing
- Move history fetching to hook
- Style balance history chart and tooltip

## [0.107.10] - 2025-02-26

### 🐛 Bug Fixes

- Single subnet transaction table filter

## [0.107.9] - 2025-02-25

### 🐛 Bug Fixes

- Default 30D && update tooltip

## [0.107.8] - 2025-02-25

### 🐛 Bug Fixes

- Reg cost chart

## [0.107.7] - 2025-02-25

### 🐛 Bug Fixes

- Single subnet left section
- Icon size

## [0.107.6] - 2025-02-25

### 🚀 Features

- Pass table selected subnet to balance history chart

### 🐛 Bug Fixes

- Background colour key
- Single subnet left section

### 🚜 Refactor

- Restore subnet balance chart
- Ensure no rounding errors generate a splodge
- Account chart styling

## [0.107.5] - 2025-02-25

### 🐛 Bug Fixes

- Subnet table ordering

## [0.107.4] - 2025-02-24

### 🐛 Bug Fixes

- Change titles of boxes
- Number formatting

## [0.107.3] - 2025-02-24

### 🐛 Bug Fixes

- Chart error

## [0.107.2] - 2025-02-24

### 🐛 Bug Fixes

- Update header menu
- Update subnets table

## [0.107.1] - 2025-02-24

### 🐛 Bug Fixes

- Subnets table

## [0.107.0] - 2025-02-24

### 🐛 Bug Fixes

- Search bar
- Remove sum on subnets table
- Subnets table

## [0.106.16] - 2025-02-24

### 🐛 Bug Fixes

- Validator table search

## [0.106.15] - 2025-02-24

### 🐛 Bug Fixes

- Minor errors
- Add validator search

## [0.106.14] - 2025-02-22

### 🐛 Bug Fixes

- Single validator transaction table
- Validators table on home page
- Subnets table on home page

## [0.106.13] - 2025-02-21

### 🐛 Bug Fixes

- Row to open in a new tab
- Single validator transaction table

## [0.106.12] - 2025-02-21

### 🚀 Features

- Add in root balance stake percent into wallet card

### 🐛 Bug Fixes

- Floating point error on multiplication
- Row to open in a new tab

### 🚜 Refactor

- Style tweaks to wallet header
- Balances hotkey is hoykey, and initial sort by tao
- Single account styling adjustments
- Skeleton when loading account card data
- Small tweaks
- Revert line in last checkin

## [0.106.11] - 2025-02-21

### 🐛 Bug Fixes

- Subnet link on transaction table

## [0.106.10] - 2025-02-21

## [0.106.9] - 2025-02-21

### 🐛 Bug Fixes

- Fix sorting on alpha holdings
- Tao Switch rendering console error
- Home page header skeleton
- Change transfers tab to transaction tab
- Dominance change %
- Remove dollar value
- Performance table header sticky
- Remove dollar from supply box
- Remove tao from total supply and replace with alpha symbol
- Subnet row to open new tab
- Click subnet to open on homepage
- Add subnet filter on transaction table
- Show root on transaction table
- Add filter amount on transaction table
- Matching M

### 🚜 Refactor

- Alpha holdings table to use local state instead of params
- Tweak top padding (design feedback)
- Remove redundant dtao account page
- Bring back sorting on alpha holdings
- Align heading

## [0.106.8] - 2025-02-21

### 🐛 Bug Fixes

- Dashboard link on mobile view

## [0.106.7] - 2025-02-20

### 🚀 Features

- Change subnet trading view to 60m interval

### 🐛 Bug Fixes

- Remove delegated balance box
- Auto refresh single subnet left section

### 🚜 Refactor

- Styling of wallet header
- Fill in subnet data in table
- Style single account page for desktop
- Swap styling
- Remove legacy components
- Remove unused call
- Rearrange charts and tables to match design

## [0.106.6] - 2025-02-20

### 🚀 Features

- Create an alpha stake balance table

### 🐛 Bug Fixes

- Remove delegated balance box

## [0.106.5] - 2025-02-20

### 🚀 Features

- Introduce new header component layout

### 🐛 Bug Fixes

- Subnet trading data card data source
- Add coldkey filtering

## [0.106.4] - 2025-02-19

### 🐛 Bug Fixes

- Subnet column
- Subnet trading data card data source

## [0.106.3] - 2025-02-19

### 🐛 Bug Fixes

- Subnet column ordering
- Subnet column

## [0.106.2] - 2025-02-19

### 🐛 Bug Fixes

- Add delegation table ordering

## [0.106.1] - 2025-02-19

### 🐛 Bug Fixes

- Metagraph table
- Single validator header section

## [0.106.0] - 2025-02-18

### 🚀 Features

- Add sum row

## [0.105.22] - 2025-02-18

## [0.105.21] - 2025-02-18

### 🚀 Features

- Extract tao switch into component

### 🐛 Bug Fixes

- Exchange vali take to CHK take

### 🚜 Refactor

- Use tao switch component on subnets table

## [0.105.20] - 2025-02-18

### 🐛 Bug Fixes

- Circulating supply box
- Single validator page

## [0.105.19] - 2025-02-18

### 🐛 Bug Fixes

- Account delegation table do not display symbols when no value
- Fix paging and sorting issues with single subnet holder table

## [0.105.18] - 2025-02-18

## [0.105.17] - 2025-02-18

### 🐛 Bug Fixes

- Metagraph table

## [0.105.16] - 2025-02-18

### 🐛 Bug Fixes

- Search subnet

## [0.105.15] - 2025-02-17

### 🐛 Bug Fixes

- Sorting key
- Move Noms

### 🚜 Refactor

- Pass down page sizes in props

## [0.105.14] - 2025-02-17

### 🐛 Bug Fixes

- Metagraph table
- Metagraph table filtering
- Validator return box

## [0.105.13] - 2025-02-17

### 🐛 Bug Fixes

- Validator return box
- Metagraph sorting
- Metagraph table

## [0.105.11] - 2025-02-17

### 🐛 Bug Fixes

- Subnet name display do not rollover if description empty as well as NA
- Fix subnet transactions paging and sorting by using state instead of search params
- Use subnet alpha symbol on holders table
- Rank based on Stake Weight
- Add Root Stake(actual)
- Update Total Nominators format
- Validator return box
- Metagraph symbol
- Metagraph table

### ⚡ Performance

- Scope single subnet details fetch to subnet

## [0.105.10] - 2025-02-17

### 🐛 Bug Fixes

- Metagrpah table
- Last 7 days chart of home page
- SubnetNameDisplay component

## [0.105.9] - 2025-02-17

## [0.105.8] - 2025-02-17

### 🐛 Bug Fixes

- Metagraph table

## [0.105.7] - 2025-02-17

### 🚀 Features

- Change txn link on single subnet transactions table to point to extrinsic
- Replace subnets table tooltip with rollover
- Subnet github open in new window
- Add tao symbol to emissions, owner, miner and validator subnet cards

### 🐛 Bug Fixes

- Row link on Subnet and Validator table
- Header dropmenu font

## [0.105.6] - 2025-02-17

### 🐛 Bug Fixes

- Adjust reg cost chart y scale to fix rendering issue in Chrome

## [0.105.5] - 2025-02-17

### 🐛 Bug Fixes

- Validators table Root Stake filter

## [0.105.4] - 2025-02-14

### 🐛 Bug Fixes

- Performance

## [0.105.3] - 2025-02-14

### 🐛 Bug Fixes

- Fix tsc issue
- Use corrrect delegate endpoint for trading data card
- Get rank from API
- Remove taostats socials from signle subnet pages
- Use alpha balance for holder %
- Hide broken cold key filter modal
- Remove child performance

### ⚡ Performance

- Filter subnet fetch when netuid supplied

## [0.105.2] - 2025-02-14

### 🐛 Bug Fixes

- Subnet balance && family balance

## [0.105.1] - 2025-02-14

### 🚀 Features

- Remove netuid prefix from subnets table

## [0.105.0] - 2025-02-14

### 🚀 Features

- Add social section

## [0.104.98] - 2025-02-14

### 🐛 Bug Fixes

- Remove snap to Y on subnet registration cost to keep in bounds
- Row default 100
- Remove subnet tab

## [0.104.96] - 2025-02-14

### 🐛 Bug Fixes

- Remove error icon

## [0.104.95] - 2025-02-14

### 🐛 Bug Fixes

- Remove error icon

## [0.104.93] - 2025-02-14

### 🚀 Features

- Subnets table Add 2dp to liquidity and volume, introduce subnet tooltip

### 🐛 Bug Fixes

- Show childkey take
- Single validator

## [0.104.92] - 2025-02-14

### 🐛 Bug Fixes

- Remove WC
- Show default 100 rows
- Add Root Stake

## [0.104.91] - 2025-02-14

### 🚀 Features

- Change tv chart default to 15m

### 🐛 Bug Fixes

- Update endpoint

## [0.104.90] - 2025-02-14

### 🐛 Bug Fixes

- Change Root Stake to Root Weight (0.18)
- Single subnet metagraph table

## [0.104.89] - 2025-02-14

### 🐛 Bug Fixes

- Header card on homepage
- Dashboard dropbox
- Topbar price color

## [0.104.88] - 2025-02-14

### 🐛 Bug Fixes

- Subnet filtering on delegation table
- Balance history chart on account page

## [0.104.87] - 2025-02-13

### 🐛 Bug Fixes

- Set limits for subnet fetches to 100

## [0.104.86] - 2025-02-13

### 🐛 Bug Fixes

- Single subnet delegation table
- Address styling issues on account delegation table

## [0.104.85] - 2025-02-13

### 🐛 Bug Fixes

- Wrong link
- Single subnet delegation table

## [0.104.84] - 2025-02-13

### 🐛 Bug Fixes

- Hide dtao menu in blockchain
- Wrong link

## [0.104.83] - 2025-02-13

### 🐛 Bug Fixes

- Hide dtao menu in blockchain

## [0.104.82] - 2025-02-13

### 🐛 Bug Fixes

- Price chart color
- Tooltip color
- Delegation table

## [0.104.81] - 2025-02-13

### 🐛 Bug Fixes

- Price chart color

## [0.104.80] - 2025-02-13

### 🚀 Features

- Remove unecessary columns and fix buy/sell
- Add extrinsic to delegation

## [0.104.79] - 2025-02-13

### 🐛 Bug Fixes

- Remove netuid column on transactions table of single subnet page

## [0.104.78] - 2025-02-13

### 🚀 Features

- Add new transactions box to home page

### 🐛 Bug Fixes

- Update subnet box on homepage
- Update validator box on home page
- Rename transactions table title to transfers
- Update CTA box postion

## [0.104.77] - 2025-02-13

### 🐛 Bug Fixes

- Home header section

## [0.104.76] - 2025-02-13

### 🚀 Features

- Additional columns for delegation table
- Include symbol for alpha
- Include subnet name
- Rearrange account tabs

### 🚜 Refactor

- Clean up comments

## [0.104.75] - 2025-02-13

### 🐛 Bug Fixes

- Add emission on subnets table
- Emission filtering on subnets table
- Subnets tab

## [0.104.74] - 2025-02-13

### 🐛 Bug Fixes

- Set max y-axis scale on the reg cost chart
- Add emission on subnets table

## [0.104.73] - 2025-02-13

### 🐛 Bug Fixes

- Change Total Daily Return to Nominator Return
- Move Root Metagraph table to Analytics Section

## [0.104.72] - 2025-02-12

### 🐛 Bug Fixes

- Blockchain menu
- Hide top validator chart

## [0.104.71] - 2025-02-12

### 🐛 Bug Fixes

- Disable hotkey link

## [0.104.70] - 2025-02-12

### 🐛 Bug Fixes

- Tab select
- Single subnet left section

## [0.104.69] - 2025-02-12

### 🐛 Bug Fixes

- Single subnet left section
- Holder table
- Tab select

## [0.104.68] - 2025-02-12

### 🚀 Features

- Strip out unecessary data for go-live
- Delegation wip
- Remove CSV export from delegation table as incorrect data
- Rename netuid to subnet account table
- Rename alpha balances tab to balances
- Retain position number when filtering

### 🐛 Bug Fixes

- Remove the age
- Subnets table order
- Hide CSV export

## [0.104.67] - 2025-02-12

### 🚀 Features

- Include dtao chart on page

### 🐛 Bug Fixes

- Add global bank && global stake

## [0.104.65] - 2025-02-12

### 🐛 Bug Fixes

- Single subnet tab hover style
- Transaction table style
- Subnet info font style

## [0.104.64] - 2025-02-12

### 🚀 Features

- Add Owner key && Reg Date

### 🐛 Bug Fixes

- Subnet Data Section
- Remove the social section

## [0.104.63] - 2025-02-11

### 🐛 Bug Fixes

- Learn more link
- Single validator table filtering

## [0.104.62] - 2025-02-11

### 🐛 Bug Fixes

- Remove console log
- Single validator page
- Validator filtering

## [0.104.61] - 2025-02-11

### 🐛 Bug Fixes

- Miss the netuid filter

## [0.104.60] - 2025-02-11

### 🚀 Features

- Add tab and port over stake balance table from Dtao account
- Include subnet name and symbol information
- Add coldkey filter modal
- Add holder table

### 🐛 Bug Fixes

- Single transaction table
- Transaction link

## [0.104.59] - 2025-02-11

### 🐛 Bug Fixes

- Remove console log

## [0.104.58] - 2025-02-11

### 🐛 Bug Fixes

- Metagraph child hotkey

## [0.104.57] - 2025-02-10

## [0.104.56] - 2025-02-10

### 🐛 Bug Fixes

- Single subnet gradient
- Card gradient
- Stake weight column
- Update metagraph table

## [0.104.55] - 2025-02-10

### 🚀 Features

- Match subnet chart colours to page scheme

### 🐛 Bug Fixes

- Six decimal && change the color

## [0.104.54] - 2025-02-10

### 🚀 Features

- Reduce margin on trading chart
- Tweak height so chart still occupies same space including the removed margin

## [0.104.53] - 2025-02-10

## [0.104.52] - 2025-02-10

### 🚀 Features

- Theme to match taostats cards
- Use log as default scale

## [0.104.50] - 2025-02-10

### 🐛 Bug Fixes

- Subnets table row cursor point
- Update metagraph table columns

## [0.104.49] - 2025-02-10

### 🐛 Bug Fixes

- Pagination

## [0.104.48] - 2025-02-10

### 🚀 Features

- Updated colour scheme and enabled some header widget options

### 🐛 Bug Fixes

- Subnets pagination

### 🚜 Refactor

- Remove unecessary rewrite
- Tidy up component

## [0.104.47] - 2025-02-07

### 🐛 Bug Fixes

- Subnets chart color
- Cursor change
- Subnets pagination

## [0.104.46] - 2025-02-07

### 🐛 Bug Fixes

- Lint
- Validator description
- Add single validator filtering

## [0.104.45] - 2025-02-07

### 🐛 Bug Fixes

- Single validator loading skeleton
- Fix validator pagination

## [0.104.44] - 2025-02-07

### 🐛 Bug Fixes

- Single validator mobile responsive

## [0.104.43] - 2025-02-07

### 🐛 Bug Fixes

- Total Stake Weight

## [0.104.42] - 2025-02-07

### 🐛 Bug Fixes

- Validator page

## [0.104.41] - 2025-02-06

### 🐛 Bug Fixes

- Update the api endpoint
- Add filter

## [0.104.40] - 2025-02-06

### Fix

- Theme settings breaking context opacity and enable chart tool

## [0.104.39] - 2025-02-06

### 🚀 Features

- Add advanced chart to single subnet page

### 🐛 Bug Fixes

- Divs column
- Remove delegation table
- Fix Updated Column
- Rows highlight
- POS pill
- Lint
- Fix lint error
- Copy deps and regen lock file
- Fix deps copy to maintain dir structure

### 🚜 Refactor

- Scope imports on advanced chart component

### ⚙️ Miscellaneous Tasks

- Pass through status code from UDF source

## [0.104.38] - 2025-02-06

## [0.104.37] - 2025-02-06

## [0.104.36] - 2025-02-06

### 🐛 Bug Fixes

- Remove the description

## [0.104.35] - 2025-02-06

### 🐛 Bug Fixes

- Change Nominators to Noms
- Remove the order select
- Line chart font size
- CK take column
- POS pill

## [0.104.34] - 2025-02-05

### 🐛 Bug Fixes

- Add order select

## [0.104.33] - 2025-02-05

### 🐛 Bug Fixes

- Mobile responsive

## [0.104.32] - 2025-02-05

### 🐛 Bug Fixes

- Proportion column

## [0.104.31] - 2025-02-05

### 🐛 Bug Fixes

- Table column text right align
- Tab style

## [0.104.30] - 2025-02-05

### 🐛 Bug Fixes

- Card line height
- Table column text right align

## [0.104.29] - 2025-02-05

### 🐛 Bug Fixes

- Single validator header

## [0.104.28] - 2025-02-05

### 🚀 Features

- Add advanced chart to single subnet page

### 🐛 Bug Fixes

- Font style

## [0.104.27] - 2025-02-05

### 🚀 Features

- Update key

### 🐛 Bug Fixes

- Line chart

## [0.104.26] - 2025-02-05

### 🚀 Features

- Update key

## [0.104.25] - 2025-02-05

### 🚀 Features

- Single validator table

### 🐛 Bug Fixes

- Fix #1029: fix card padding

## [0.104.24] - 2025-02-04

### 🐛 Bug Fixes

- Fix #1029: fix card padding

## [0.104.23] - 2025-02-04

### 🐛 Bug Fixes

- Fix #1029: registration Data chart
- Fix #1029: fix symbol

## [0.104.22] - 2025-02-04

### 🐛 Bug Fixes

- Fix #1029: show the percentage
- Fix #1029: subnet tab
- Fix #1029: description uppercase
- Fix #1029: table USD column sort

## [0.104.21] - 2025-02-04

### 🐛 Bug Fixes

- Fix #1029: box size
- Fix #1029: fix gradient
- Fix #1029: add social section

## [0.104.20] - 2025-02-04

### 🐛 Bug Fixes

- Fix #1029: fix miner weights chart label

## [0.104.19] - 2025-02-04

### 🐛 Bug Fixes

- Fix #1029: chart mobile responsive

## [0.104.18] - 2025-02-04

### 🐛 Bug Fixes

- Fix #1029: remove the chart title
- Fix #1029: remove the horizontal scrollbar

## [0.104.17] - 2025-02-03

### 🐛 Bug Fixes

- Fix #1029: fix last 7 days chart

## [0.104.16] - 2025-02-03

### 🐛 Bug Fixes

- Fix #1029: subnetTab

## [0.104.15] - 2025-02-03

### 🐛 Bug Fixes

- Fix #1029: fix subnet tab

## [0.104.14] - 2025-02-03

### 🐛 Bug Fixes

- Fix #1029: fix price format
- Fix #1029: fix circulating supply
- Fix #1029: fix wallet card
- Fix #1029: add trading section
- Fix #1029: subnet data api integration

## [0.104.13] - 2025-02-03

### 🐛 Bug Fixes

- Fix #1029: mobile responsive && Market Cap
- Fix #1029: age format

## [0.104.12] - 2025-01-31

### 🚀 Features

- Feat #1029: add tablist

### 🐛 Bug Fixes

- Update switch

## [0.104.11] - 2025-01-31

### 🐛 Bug Fixes

- Api integration for single subnet page
- Fix #1029: update transaction table

## [0.104.10] - 2025-01-31

### 🚀 Features

- Feat #1029: single subnet page
- Feat #1029: add transaction table

### 🐛 Bug Fixes

- Update layout of the single subnet page
- Fix #1029: set the default list to display 100 subnets
- Fix #1029: update the spacing for mobile version
- Fix #1029: fix search

## [0.104.9] - 2025-01-30

### 🐛 Bug Fixes

- Fix table column issue & age value

## [0.104.8] - 2025-01-30

### 🐛 Bug Fixes

- Fix #1029: fix feedbacks
- Fix #1029: subnets table columns

## [0.104.7] - 2025-01-30

### 🐛 Bug Fixes

- Fix #1029: fix feedbacks

## [0.104.6] - 2025-01-30

### 🚀 Features

- Feat #1029: update filtering

### 🐛 Bug Fixes

- Fix mog's feedback

## [0.104.5] - 2025-01-29

### 🚀 Features

- Feat #1029: update search filter
- Feat #1029: show tooltip

## [0.104.4] - 2025-01-29

### 🚀 Features

- Feat #1029: update price format
- Feat #1029: update search filter

## [0.104.3] - 2025-01-29

### 🚀 Features

- Feat #1029: update age format

## [0.104.2] - 2025-01-29

### 🚀 Features

- Feat #1029: fix build error
- Feat #1029: update the table columns

## [0.104.1] - 2025-01-29

### 🚀 Features

- Feat #1029: fix row selector

## [0.104.0] - 2025-01-28

### 🚀 Features

- Feat #1029: update the table column

## [0.103.1] - 2025-01-28

### 🚀 Features

- Feat #1029: pagination style

## [0.103.0] - 2025-01-28

### 🚀 Features

- Feat #1029: update pagination

## [0.102.0] - 2025-01-28

### 🚀 Features

- Feat #1029: update the footer

## [0.101.0] - 2025-01-27

### 🚀 Features

- Feat #1029: subnets table

## [0.100.0] - 2025-01-27

### 🚀 Features

- Update subnet table

## [0.99.0] - 2025-01-27

### 🚀 Features

- Update pool api endpoint
- Add subnet table
- Last 7 days chart on subnets
- Subnets table
- Update subnets table

## [0.98.3] - 2025-01-23

### 🚀 Features

- Feat #1000: update selector
- Feat #1000: fix selector

## [0.98.2] - 2025-01-22

### 🚀 Features

- Feat #1000: update selector

## [0.98.1] - 2025-01-22

### 🚀 Features

- Feat #1000: re-render error

## [0.98.0] - 2025-01-21

### 🚀 Features

- Feat #1000: account details table
- Feat #1000: account detail chart
- Feat #1000: netuid & hotkey selector

## [0.97.4] - 2025-01-21

### 🐛 Bug Fixes

- Update child key take in child performance card
- Add child key take to table

## [0.97.3] - 2025-01-21

### 🐛 Bug Fixes

- Update child key take in child performance card

## [0.97.2] - 2025-01-21

### 🐛 Bug Fixes

- Fix take for child performance card

## [0.97.1] - 2025-01-20

### 🚀 Features

- Validator Child Performance

## [0.97.0] - 2025-01-20

### 🚀 Features

- Feat #933: update the api endpoint
- Feat #933: add Hotkey Alpha Shares Table in Dtao
- Feat #933: add Coldkey Alpha Shares Tabls in Dtao
- Feat #933: Add Stake Balance Table on Dtao
- Feat #933: Add Stake Balance Aggregated Table in Dtao

## [0.95.8] - 2025-01-18

### 🐛 Bug Fixes

- Show dtao sub menu

## [0.95.7] - 2025-01-18

### 🐛 Bug Fixes

- Deviation table error

## [0.95.6] - 2025-01-18

### 🐛 Bug Fixes

- Stake Details Page

## [0.95.5] - 2025-01-17

### 🐛 Bug Fixes

- Disable dtao menu
- Menu style

## [0.95.4] - 2025-01-17

### 🐛 Bug Fixes

- Thumbnail

## [0.95.3] - 2025-01-17

### 🐛 Bug Fixes

- Fix #979: add commit reveal epochs to the hyperparameters page

## [0.95.2] - 2025-01-16

### 🐛 Bug Fixes

- Fix #970: incorrect sorting

## [0.95.1] - 2025-01-16

### 🚀 Features

- Feat #933: add delegation table in dtao
- Feat #933: add pool table in dtao
- Feat #933: submenu
- Feat #933: add Subnet Emission table in dtao
- Feat #933: add hotkey shares table in dtao
- Feat #933: add Hotkey Emission Table in Dtao
- Feat #933: add Hotkey Alpha Table in Dtao
- Feat #933: add coldkey Shares Table in Dtao
- Feat #933: update Delegation Table in Dtao
- Feat #933: update the sub menu
- Feat #933: update Pool Table in Dtao

## [0.94.3] - 2025-01-16

### 🐛 Bug Fixes

- Transaction status

## [0.94.2] - 2025-01-16

### 🐛 Bug Fixes

- Remove the HistoricalWeight Chart

## [0.94.1] - 2025-01-16

### 🐛 Bug Fixes

- Update transaction status

## [0.94.0] - 2025-01-14

### 🚀 Features

- Feat #952: Add historical weights for each subnet
- Feat #952: add the calendar

## [0.93.0] - 2025-01-14

### 🚀 Features

- Feat #964: 5coz validator to latent holdings

## [0.92.5] - 2025-01-13

### 🐛 Bug Fixes

- Analytics Emissions Chart

## [0.92.4] - 2025-01-13

### 🐛 Bug Fixes

- Re-render chart

## [0.92.3] - 2025-01-13

### 🐛 Bug Fixes

- Build error

## [0.92.2] - 2025-01-13

### 🐛 Bug Fixes

- Weekly extrinsic success and failures chart
- Tooltips && label

## [0.92.1] - 2025-01-10

### 🐛 Bug Fixes

- CTA Box

## [0.92.0] - 2025-01-09

### 🚀 Features

- Change deviation color

## [0.91.0] - 2025-01-09

### 🐛 Bug Fixes

- Daily price changes chart
- Add daily price chart on analytics menu
- Tao Price Percentage Chanage Chart
- Daily price chart

## [0.90.0] - 2025-01-08

### 🚀 Features

- Add Validator Weights Deviation table

## [0.89.16] - 2025-01-08

### 🐛 Bug Fixes

- Remove the z-index in root metagraph
- Subnet totals to blue for header cols

## [0.89.15] - 2025-01-08

### 🐛 Bug Fixes

- Analytics menu

## [0.89.14] - 2025-01-08

### 🐛 Bug Fixes

- Show z-index in root metagraph

## [0.89.13] - 2025-01-08

### 🐛 Bug Fixes

- Root metagraph
- Analytics menu

## [0.89.12] - 2025-01-08

### 🐛 Bug Fixes

- Root metagraph

## [0.89.11] - 2025-01-08

### 🐛 Bug Fixes

- Hidden z-index in root metagraph
- Enable analytics chart api

## [0.89.10] - 2025-01-08

### 🐛 Bug Fixes

- Hidden z-index in root metagraph

## [0.89.9] - 2025-01-08

### 🐛 Bug Fixes

- Staking Calculator

## [0.89.8] - 2025-01-08

### 🐛 Bug Fixes

- Z-index on root metagraph

## [0.89.7] - 2025-01-07

### 🐛 Bug Fixes

- Root Metagraph Table
- Avg_wight on root metagraph

## [0.89.6] - 2025-01-07

### 🐛 Bug Fixes

- Subnets Growth Chart

## [0.89.5] - 2025-01-07

### 🐛 Bug Fixes

- Subnet Growth Chart

## [0.89.4] - 2025-01-07

### 🐛 Bug Fixes

- Add the spacing
- Subnet Growth

## [0.89.3] - 2025-01-06

### 🐛 Bug Fixes

- Remove the redirect exchange page

## [0.89.2] - 2025-01-06

### 🐛 Bug Fixes

- Add the CTA Box & Remove the title

## [0.89.1] - 2025-01-06

### 🐛 Bug Fixes

- Show z-index in root metagraph table

## [0.89.0] - 2025-01-06

### 🚀 Features

- Feat #894: show EVM account with checksum

### 🐛 Bug Fixes

- Fix #887: remove height on EVM contracts table

## [0.88.2] - 2025-01-06

### 🐛 Bug Fixes

- Fix #887: remove height on EVM contracts table

## [0.88.1] - 2025-01-04

### 🐛 Bug Fixes

- Address column link in contracts page

## [0.88.0] - 2025-01-03

### 🚀 Features

- Feat #859: 18 decimal place

## [0.87.0] - 2025-01-03

### 🚀 Features

- Feat #859: change amount on the tx table

## [0.86.0] - 2025-01-03

### 🚀 Features

- Feat #859: account page address area
- Feat #859: transaction table header

## [0.85.1] - 2025-01-03

### 🐛 Bug Fixes

- EVM Transaction Detail page

## [0.85.0] - 2025-01-03

### 🚀 Features

- Feat #859: EVM Account page

## [0.84.2] - 2025-01-03

### 🐛 Bug Fixes

- Amount column in evm transaction table

## [0.84.1] - 2025-01-03

### 🐛 Bug Fixes

- Amount in evm transaction page

## [0.84.0] - 2025-01-03

### 🚀 Features

- Add data-current-supply and data-max-supply endpoints
- Feat #855: add result column to EVM transactions

## [0.83.0] - 2024-12-30

### 🚀 Features

- Add contract page

## [0.82.1] - 2024-12-30

### 🚀 Features

- Remove the icon

### 🐛 Bug Fixes

- Add redirection to evm transaction page

## [0.82.0] - 2024-12-30

### 🚀 Features

- Feat #749: daily number of subnets 365 days
- Update blockchain & evm page

## [0.81.0] - 2024-12-30

### 🚀 Features

- Feat #749: daily number of subnets 365 days
- Feat #749: update daily number of subnets
- Feat #749: update the height

## [0.80.4] - 2024-12-27

### 🐛 Bug Fixes

- Fix circulating free in the tokenomics graph

## [0.80.3] - 2024-12-24

### 🐛 Bug Fixes

- Update tao5 logo

## [0.80.2] - 2024-12-23

### 🐛 Bug Fixes

- Fix #826: add kraken logo

## [0.80.1] - 2024-12-20

### 🐛 Bug Fixes

- Fix #821: Update tatsu Validator Icon

## [0.80.0] - 2024-12-19

### 🚀 Features

- Feat #775: change Transfers to Transactions
- Feat #775: add Method and Input fields

## [0.79.3] - 2024-12-19

### 🚀 Features

- Feat #775: blocks is not working && URL structure

### 🐛 Bug Fixes

- Extrinsic details amount

## [0.79.2] - 2024-12-19

### 🐛 Bug Fixes

- Fix #809: Show Error Data on Calls and Extrinsics

## [0.79.1] - 2024-12-19

### 🐛 Bug Fixes

- Total Accounts on Dashboard Page

## [0.79.0] - 2024-12-18

### 🚀 Features

- Feat #775: add EVM data
- Feat #775: update blocks table

## [0.78.8] - 2024-12-18

### 🐛 Bug Fixes

- Circulating supply

## [0.78.7] - 2024-12-17

### 🐛 Bug Fixes

- Fix extrinsic page redirection

## [0.78.6] - 2024-12-17

### 🐛 Bug Fixes

- Circulating supply

## [0.78.5] - 2024-12-17

### 🐛 Bug Fixes

- Fix Emission Weighting Events color
- Emission Weighting Events Table Color

## [0.78.4] - 2024-12-16

### 🐛 Bug Fixes

- Fix Emission Weighting Events color

## [0.78.3] - 2024-12-16

## [0.78.2] - 2024-12-16

### 🐛 Bug Fixes

- Fix circulating, staked values in the tokenomics chart

## [0.78.1] - 2024-12-16

## [0.78.0] - 2024-12-16

### 🚀 Features

- Feat #784: Update validator information

## [0.77.1] - 2024-12-13

### 🐛 Bug Fixes

- Fix : incorrect circulating

## [0.77.0] - 2024-12-13

### 🚀 Features

- Feat #737: Weekly Extrinsic Success and Failures Chart

## [0.76.1] - 2024-12-12

### 🐛 Bug Fixes

- Fix #738: x axios label
- Incorrect circulating supply

## [0.76.0] - 2024-12-12

### 🚀 Features

- Feat #738: Success to Failure Ratio Chart

## [0.75.12] - 2024-12-12

### 🐛 Bug Fixes

- Fix incorrect circulating supply on the tokenomics page

## [0.75.11] - 2024-12-11

### 🐛 Bug Fixes

- Fix event page error
- Fix event timestamp error

## [0.75.10] - 2024-12-11

### 🐛 Bug Fixes

- Underline color

## [0.75.9] - 2024-12-11

### 🐛 Bug Fixes

- Metagraph underline color

## [0.75.8] - 2024-12-10

### 🐛 Bug Fixes

- Fix #729: 404 error on block explorer
- Update delegation balance api endpoint

## [0.75.7] - 2024-12-09

### 🐛 Bug Fixes

- Root metagraph

## [0.75.6] - 2024-12-09

### 🐛 Bug Fixes

- Last reward block

## [0.75.5] - 2024-12-09

### 🐛 Bug Fixes

- Account details chart

## [0.75.4] - 2024-12-09

### 🐛 Bug Fixes

- Weights placed by validators for each miner chart

## [0.75.3] - 2024-12-09

### 🐛 Bug Fixes

- Metagraph underline

## [0.75.2] - 2024-12-09

### 🐛 Bug Fixes

- Fix #729: display event on Event tab
- Fix #729: extrinsics tab page 2 that does not exist

## [0.75.1] - 2024-12-06

### 🐛 Bug Fixes

- Fix #728: register network extrinsic page error

## [0.75.0] - 2024-12-06

### 🚀 Features

- Feat #680: update the root subnet chart
- Feat #680: update the root subnet chart with color based

## [0.74.0] - 2024-12-05

### 🚀 Features

- Feat #680: update the root subnet chart

## [0.73.5] - 2024-12-05

### 🐛 Bug Fixes

- Fix #721: hex keys on hash page

## [0.73.4] - 2024-12-05

### 🐛 Bug Fixes

- Standardize the order and placement of the CSV button
- Smooth scrolling behavior
- Dynamic Section Highlighting
- Add skeleton
- Rounding on the subnet registration tab graph
- Historical emission table
- Minor errors
- Searching hex keys often results

## [0.73.3] - 2024-12-04

### 🐛 Bug Fixes

- Historical emission table

## [0.73.2] - 2024-12-04

### 🐛 Bug Fixes

- Ui enhancements
- Validator chart
- Historical emission table
- Small errors

## [0.73.1] - 2024-12-03

### 🐛 Bug Fixes

- Ui enhancements

## [0.73.0] - 2024-12-03

### 🚀 Features

- Feat #593: subnet menu

### 🐛 Bug Fixes

- Sudo ui enhancements

## [0.72.2] - 2024-12-03

### 🐛 Bug Fixes

- Build error
- Historical emission weighting events
- Fix #694: ui enhancements for block interaction

## [0.72.1] - 2024-12-02

### 🐛 Bug Fixes

- Update the title for both chart and new table
- Update api endpoint

## [0.72.0] - 2024-12-02

### 🚀 Features

- Feat #681: last 10 weights set by the validator

### 🐛 Bug Fixes

- Filter extrinsics

## [0.71.0] - 2024-11-29

### 🚀 Features

- Feat #667: leverage the event table to filter and display all sudo events
- Feat #667: leverage the event table to filter and display all sudo ev…

### 🐛 Bug Fixes

- Add sudo table column

## [0.70.0] - 2024-11-28

### 🚀 Features

- Feat #465: create main page for analytics section

## [0.69.0] - 2024-11-27

### 🚀 Features

- Feature #666: fix navigation
- Feat #666: Update table label

## [0.68.0] - 2024-11-27

### 🚀 Features

- Feat #666: create runtime upgrade events page

## [0.67.2] - 2024-11-27

### 🐛 Bug Fixes

- Fix #668: northtensor validator image

## [0.67.1] - 2024-11-27

### 🐛 Bug Fixes

- Fix new validator stuff

## [0.67.0] - 2024-11-27

### 🚀 Features

- Feat #235: add miner comparison weights chart

## [0.66.0] - 2024-11-26

### 🚀 Features

- Feat #219: add miner visualization chart

## [0.65.5] - 2024-11-26

### 🚀 Features

- If subnet hyperparam `registration_enabled` is false then add a label at top of subnet metagraph to say registration is disabled.

### 🐛 Bug Fixes

- Fix hotkey is overflowing out of the box
- Fix lint errors
- Only append an S to the breadcrumb link if the current segment is not a number
- Fix analytics chart label style
- Issue with subnet reg disabled label being set within in headerDescriptionCard
- Fix update block style
- Fix reg is disabled
- Fix text on subnet miner weights is incorrect
- Fix #655: update cortext validator image

### ⚙️ Miscellaneous Tasks

- Add react-query-devtools
- Remove package-lock.json as we're using bun for package management

## [0.65.1] - 2024-11-22

### 🐛 Bug Fixes

- Fix #628: fix single extrinsic page error

## [0.65.0] - 2024-11-22

### 🚀 Features

- Feat #210: add latest transaction chart

## [0.64.0] - 2024-11-22

### 🚀 Features

- Feat #211: add price percentage chart

## [0.63.4] - 2024-11-21

### 🐛 Bug Fixes

- Fix #627: Remove unnecessary network dropdown

## [0.63.3] - 2024-11-21

### 🐛 Bug Fixes

- Fix validator icon issue
- Make logo image on validator page sharper
- Update validator icon image

### ⚙️ Miscellaneous Tasks

- Move dev dependencies into devDependencies object.
- Fix some dependency issues

## [0.63.1] - 2024-11-21

### 🐛 Bug Fixes

- Update chart label
- Update validator icon for 5HEo565WAy4Dbq3Sv271SAi7syBSofyfhhwRNjFNSM2gP9M2

## [0.63.0] - 2024-11-21

### 🚀 Features

- Feat #212: add daily price change chart

## [0.62.4] - 2024-11-21

### 🐛 Bug Fixes

- Fix address encoding issue
- Update foundry validator icon

## [0.62.3] - 2024-11-20

### 🐛 Bug Fixes

- Fix address encoding issue on extrinsic page

## [0.62.2] - 2024-11-20

### 🐛 Bug Fixes

- Fix account page chart showing zeroed out data
- Ensure account chart shows latest data if no history data exists
- Fix #607: Fix sorting issue on root metagraph table

## [0.62.0] - 2024-11-20

### 🚀 Features

- Feat #209: Add largest 100 transactions chart

## [0.61.5] - 2024-11-20

### 🚀 Features

- Update coldkey swap labels in wallet header

### 🐛 Bug Fixes

- Fix issue with coldkeyswap copy button copying wrong value to clipboard
- Add a 2nd y axis for emissions

### ⚙️ Miscellaneous Tasks

- Update gitignore

## [0.61.3] - 2024-11-19

### 🐛 Bug Fixes

- Fix balance history chart area issue

## [0.61.2] - 2024-11-19

### 🐛 Bug Fixes

- Fix #217: fix sorting issue of account API

## [0.61.1] - 2024-11-19

### 🐛 Bug Fixes

- Fix balance history chart issue

## [0.61.0] - 2024-11-19

### 🚀 Features

- Feat #236: Update timestamp range in query params
- Feat #217: add top account balance chart

## [0.60.0] - 2024-11-19

### 🚀 Features

- Feat #236: Add emission vs recycled chart

## [0.59.0] - 2024-11-18

### 🚀 Features

- Feat #216: add recent accounts chart

## [0.58.0] - 2024-11-18

### 🚀 Features

- Feat #586: add coldkey swapped key on single account page

## [0.57.6] - 2024-11-15

### 🐛 Bug Fixes

- Remove weightcopier address

## [0.57.5] - 2024-11-14

### 🐛 Bug Fixes

- Fix #579: fix incorrect extrinsic url error

## [0.57.4] - 2024-11-14

### 🚀 Features

- Feat #579: update weightcopier list & update size of validator icon

## [0.57.3] - 2024-11-13

### 🐛 Bug Fixes

- Fix minor issues

## [0.57.2] - 2024-11-13

### 🐛 Bug Fixes

- Fix #574: fix weights chart and emission table

## [0.57.1] - 2024-11-12

### 🐛 Bug Fixes

- Fix #555: Update analytics chart sections

## [0.57.0] - 2024-11-12

### 🚀 Features

- Feat #568: add toggle button

## [0.56.0] - 2024-11-12

### 🚀 Features

- Feat #524: add auto-refresh button

## [0.55.4] - 2024-11-11

### 🐛 Bug Fixes

- Fix #564: fix chain issue

## [0.55.3] - 2024-11-11

### 🐛 Bug Fixes

- Fix blocks until next reward on single validator page

## [0.55.2] - 2024-11-08

### 🐛 Bug Fixes

- Fix signer address format on extrinsic table

## [0.55.1] - 2024-11-08

### 🐛 Bug Fixes

- Fix #550: add network & update account history chart

## [0.55.0] - 2024-11-08

### 🚀 Features

- Feat #551: add network filter to event & extrinsic table

## [0.54.0] - 2024-11-08

### 🚀 Features

- Feat #550: Update account page

## [0.53.1] - 2024-11-08

### 🐛 Bug Fixes

- Fix #549: Remove row color on validator performance table

## [0.53.0] - 2024-11-08

### 🚀 Features

- Feat #489: add new subnet hyperparams

## [0.52.0] - 2024-11-07

### 🚀 Features

- Feat #545: add new icon for neos foundation validator

## [0.51.0] - 2024-11-07

### 🚀 Features

- Feat #204: add block since registration vs. incentive chart

## [0.50.0] - 2024-11-06

### 🚀 Features

- Feat #204: Complete block vs incentive chart

### 🐛 Bug Fixes

- Fix subnet name error

## [0.49.2] - 2024-11-06

### 🐛 Bug Fixes

- Fix subnet name undefined error

## [0.49.1] - 2024-11-05

### 🚀 Features

- Feat #485: Update analytics page with feedback
- - graph boxes seem a little too large they could be more compact
- - as far as the colours are concerned, there is a noticeable difference between 'Top Exchanges' and 'Stats History' which breaks the visual uniformity a little, it would be nice to have a more harmonised palette for all the charts.
- - would be possible to rearrange the exchanges in the chart by descending transaction volume pls

## [0.49.0] - 2024-11-05

### 🚀 Features

- Feat #502: add parent stake to child hotkey performance box

## [0.48.0] - 2024-11-05

## [0.47.0] - 2024-11-05

### 🚀 Features

- Feat #533: Add validator take to validator page

### 🐛 Bug Fixes

- Fix #527: fix extrinsic API call on validator page

## [0.46.2] - 2024-11-04

### 🐛 Bug Fixes

- Fix #527: fix cache miss urls

## [0.46.1] - 2024-11-04

### 🐛 Bug Fixes

- Fix #526: fix metagraph table error on subnet page

## [0.46.0] - 2024-11-04

### 🚀 Features

- Feat #485: add csv download button & date range button for chart

## [0.45.0] - 2024-11-03

### 🚀 Features

- Feat #519: Add a new weight copier key

### 🐛 Bug Fixes

- Subnet keys

## [0.43.4] - 2024-11-01

### 🐛 Bug Fixes

- Subnet active keys display
- Remove unnecessary query params in API call

## [0.43.3] - 2024-10-31

### 🐛 Bug Fixes

- Remove trailing ? from uri
- Fix subnet distribution page error & update limit in APIs

## [0.43.2] - 2024-10-31

### 🚀 Features

- Feat #492: add sentry tracing

## [0.43.1] - 2024-10-29

### 🐛 Bug Fixes

- Fix initial loading of validator page & sorting of validator weights chart

## [0.44.0] - 2024-10-29

### 🚀 Features

- Feat #206: Add hourly view to block chart

## [0.43.0] - 2024-10-29

### 🚀 Features

- Feat #313: Update blocks until next rewards

## [0.42.0] - 2024-10-29

### 🚀 Features

- Feat #213: add validator weights chart

## [0.41.1] - 2024-10-28

### 🐛 Bug Fixes

- Fix filtering error on delegation table of validator page

## [0.41.0] - 2024-10-28

### 🚀 Features

- Feat #206: add daily block count chart

## [0.40.1] - 2024-10-28

### 🐛 Bug Fixes

- Fix #485: fix analytics & chart UI issue

## [0.40.0] - 2024-10-24

### 🚀 Features

- Feat #313: add additional fields to validator and hotkey page

## [0.39.2] - 2024-10-23

### 🐛 Bug Fixes

- Fix tooltip issue of miner emission chart

## [0.39.1] - 2024-10-23

### 🐛 Bug Fixes

- Fix validator table error

## [0.39.0] - 2024-10-23

### 🚀 Features

- Feat #208: add stats history chart

## [0.38.1] - 2024-10-23

### 🐛 Bug Fixes

- Fix subnet header loading issue

## [0.37.7] - 2024-10-22

### 🐛 Bug Fixes

- Fix nominator table in validator page

## [0.37.6] - 2024-10-22

### 🐛 Bug Fixes

- Fix number input issue

## [0.37.5] - 2024-10-22

### 🐛 Bug Fixes

- Fix tooltip issue of staking chart & number input on staking page

## [0.37.4] - 2024-10-22

### 🐛 Bug Fixes

- Fix price API call

## [0.37.3] - 2024-10-22

### 🐛 Bug Fixes

- Fix #471: fix staking graph

### ◀️ Revert

- Revert env config changes

## [0.35.7] - 2024-10-18

### 🚀 Features

- Wallet app terms and privacy

## [0.38.0] - 2024-10-22

### 🚀 Features

- Feat #203: Complete top exchange transaction chart

### 🐛 Bug Fixes

- Fix server side API call

## [0.37.1] - 2024-10-20

### 🐛 Bug Fixes

- Fix #223: fix extrinsic & event filtering

## [0.37.0] - 2024-10-20

### 🚀 Features

- Feat #419: update extrinsic page

## [0.36.0] - 2024-10-18

### 🚀 Features

- Feat #376: add network type selector

## [0.35.6] - 2024-10-18

### 🐛 Bug Fixes

- Fix #462: add time selector to the validator performance graph

## [0.35.5] - 2024-10-17

### 🐛 Bug Fixes

- Fix #458: fix staking page issue
- Fix tooltip position of weight graph

## [0.35.4] - 2024-10-17

### 🐛 Bug Fixes

- Remove date range selector on the validator performance chart

## [0.35.3] - 2024-10-17

### 🐛 Bug Fixes

- Fix #450: fix column count of validator performance cards

## [0.35.2] - 2024-10-17

### 🐛 Bug Fixes

- Fix #450: fix validator performance & subnet emission issue

## [0.35.1] - 2024-10-17

### 🚀 Features

- App privacy and terms pages wip

### 🐛 Bug Fixes

- Fix #447: fix underline color mismatch

## [0.35.0] - 2024-10-17

### 🚀 Features

- Feat #353: add icon to app metadata

## [0.33.3] - 2024-10-17

### 🐛 Bug Fixes

- Fix #296: fix child hotkey link on child performance page

## [0.33.2] - 2024-10-16

### 🐛 Bug Fixes

- Fix #360: fix hotkey key copy issue

## [0.33.1] - 2024-10-16

### 🐛 Bug Fixes

- Fix build error
- Fix #437: fix registration chart

## [0.33.0] - 2024-10-15

### 🚀 Features

- Feat #360: Add child performance tab

## [0.32.1] - 2024-10-15

### 🐛 Bug Fixes

- Add a redirect to not found page

## [0.32.0] - 2024-10-15

### 🚀 Features

- Feat #296: update card & table view for validator performance

## [0.31.7] - 2024-10-15

### 🐛 Bug Fixes

- Fix #422: fix displaying full address in mobile view
- Fix #422: fix sorting of event & extrinsic tables

## [0.31.6] - 2024-10-15

### 🐛 Bug Fixes

- Remove playwright dependency installation
- Fix #389: fix extrinsic order on the single block page

## [0.31.5] - 2024-10-15

### 🐛 Bug Fixes

- Fix #423: fix text overflow on staking page

## [0.31.4] - 2024-10-14

### 🐛 Bug Fixes

- Fix #347: disable extrinsic name filtering

## [0.31.3] - 2024-10-14

### 🐛 Bug Fixes

- Fix #422: fix copy button size on mobile

## [0.31.2] - 2024-10-14

### 🐛 Bug Fixes

- Fix #423: fix text overflow on staking page

## [0.31.1] - 2024-10-14

### 🐛 Bug Fixes

- Fix #388: fix daily reward calculation on coldkey page

## [0.31.0] - 2024-10-14

### 🚀 Features

- Feat #345: Upgrade to new subnet API

## [0.30.0] - 2024-10-11

### 🚀 Features

- Feat #414: Update single extrinsic page

## [0.29.0] - 2024-10-11

### 🚀 Features

- Feat #414: Create a conditional view for the extrinsic page

## [0.28.1] - 2024-10-10

### 🐛 Bug Fixes

- Fix coldkey page issue & extrinsic page redirection

## [0.28.0] - 2024-10-10

### 🚀 Features

- Feat #345: Upgrade to new Validator API endpoint
- Feat #345: Upgrade to new validator weight API

### 🐛 Bug Fixes

- Fix errors due to validator API upgrade

## [0.27.0] - 2024-10-09

### 🚀 Features

- Feat #394: Add transaction detail page

## [0.26.3] - 2024-10-09

### 🐛 Bug Fixes

- Fix title font style of miner tab

## [0.26.2] - 2024-10-09

### 🐛 Bug Fixes

- Fix #394: fix hash url redirect

## [0.26.1] - 2024-10-09

### 🐛 Bug Fixes

- Fix #399: fix incorrect title

## [0.26.0] - 2024-10-08

### 🚀 Features

- Feat #345: Upgraded to new Delegate API

## [0.25.4] - 2024-10-08

## [0.25.3] - 2024-10-08

### 🐛 Bug Fixes

- Fix #387: fix staking interest chart

## [0.25.2] - 2024-10-08

### 🐛 Bug Fixes

- Fix #391: fix tradingview button error

## [0.25.1] - 2024-10-07

### 🐛 Bug Fixes

- Fix #390: fix miner distribution chart issue
- Fix #390: fix hydration error on homepage

## [0.25.0] - 2024-10-07

### 🐛 Bug Fixes

- Fix #382: fix real-time update for tao price & upgrade to new Price API

## [0.24.2] - 2024-10-04

## [0.24.1] - 2024-10-04

### 🚀 Features

- Feat #214: Add validator emission table and weights chart

### 🐛 Bug Fixes

- Fix tooltip position of weights chart
- Fix #214: Resolve feedback

## [0.23.3] - 2024-10-03

### 🐛 Bug Fixes

- Fix #364: fix nominator value on staking page

## [0.23.2] - 2024-10-03

### 🐛 Bug Fixes

- Fix #364: fix total stake on coldkey page

## [0.23.1] - 2024-10-03

### 🐛 Bug Fixes

- Fix #374: Remove Axis from weight copier list

## [0.23.0] - 2024-10-02

### 🚀 Features

- Feat #374: Add weight copier

## [0.22.3] - 2024-10-02

### 🐛 Bug Fixes

- Fix initial loading of the table

## [0.22.2] - 2024-10-02

### 🐛 Bug Fixes

- Fix #289: Fix pagination in the extrinsic and event table

## [0.22.1] - 2024-10-02

### 🐛 Bug Fixes

- Fix #368: Fix transfer table pagination issue

## [0.22.0] - 2024-10-01

### 🚀 Features

- Feat #369: Add eslint config & fix lint error

## [0.21.0] - 2024-10-01

### 🚀 Features

- Feat #359: Add table view for validator performance

## [0.20.1] - 2024-10-01

### 🐛 Bug Fixes

- Fix #345: fix single extrinsic page error

## [0.20.0] - 2024-10-01

### 🚀 Features

- Feat #345: Upgraded to new stats API endpoint
- Feat #345: Upgraded to new extrinsic API endpoint

## [0.19.0] - 2024-09-30

### 🚀 Features

- Upgrade to new Event API endpoint

## [0.18.1] - 2024-09-30

### 🐛 Bug Fixes

- Update get function

## [0.18.0] - 2024-09-30

### 🐛 Bug Fixes

- Updated to new API endpoint URL

## [0.17.1] - 2024-09-30

### 🐛 Bug Fixes

- Fix time display issue on event page

## [0.17.0] - 2024-09-30

### 🚀 Features

- Axis temporary override
- Feat #345: Upgrade to new Block API

## [0.16.2] - 2024-09-28

### 🐛 Bug Fixes

- Fix parent stake on child hotkey page

## [0.16.1] - 2024-09-27

### 🐛 Bug Fixes

- Fix take value of child hotkey in child hotkey page

## [0.16.0] - 2024-09-27

### 🚀 Features

- Feat #345: Upgrade to new metagraph API
- Update child hotkey page

## [0.15.0] - 2024-09-26

### 🚀 Features

- Feat #345: Upgrade to new transfer API

## [0.14.0] - 2024-09-26

### 🚀 Features

- Feat #345: Upgrade to new Hotkey API

## [0.13.1] - 2024-09-26

### 🚀 Features

- Feat #345: Upgrade to new Account API

### 🐛 Bug Fixes

- Bad subnet redirects

## [0.12.10] - 2024-09-25

### 🐛 Bug Fixes

- Fix #243: Fix Cursor Alignment

## [0.12.9] - 2024-09-25

## [0.12.8] - 2024-09-25

### 🐛 Bug Fixes

- Depth exceeded render loop

## [0.12.7] - 2024-09-25

### 🐛 Bug Fixes

- Fix #339: fix main key link on coldkey page

## [0.12.6] - 2024-09-25

### 🐛 Bug Fixes

- Fix API url config

## [0.12.5] - 2024-09-25

### 🐛 Bug Fixes

- Remove weightcopier toggle & fix build error
- Fix deployment in main
- Fix #332: fix xAccessor type error in chart rendering
- Fix #332: Fix type error
- Build error
- Fix inconsistency between validator table and menu list
- Validator type error

## [0.11.0] - 2024-09-24

### 🚀 Features

- Feat #277: Add link to the docs

## [0.10.0] - 2024-09-23

### 🚀 Features

- Feat #323: Add a toggle for autorefresh

## [0.9.3] - 2024-09-23

### 🐛 Bug Fixes

- Hydration issues
- Fix #324: keep filters added in the filter bar

## [0.9.2] - 2024-09-20

### 🐛 Bug Fixes

- Changed default interval time selector

## [0.9.1] - 2024-09-20

### 🐛 Bug Fixes

- Fix #322: Direct hex key account access issue

## [0.9.0] - 2024-09-19

### 🚀 Features

- Feat #264: Add 'ALL' option

## [0.8.4] - 2024-09-19

### 🐛 Bug Fixes

- Fix #317: Convert all display times to UTC
- Fix #319: Update metagraph filter

## [0.8.2] - 2024-09-18

### 🐛 Bug Fixes

- Fix #287: Fix copy confirmation icon on single validator & subnet page

## [0.8.1] - 2024-09-18

### 🚀 Features

- Feat #302: Add an anchor in the parket/child hotkey page for each subnet
- Add port to axon ip address in table

### 🐛 Bug Fixes

- Fix #300: Update metagraph table column of hotkey page

## [0.8.0] - 2024-09-18

## [0.7.0] - 2024-09-18

### 🚀 Features

- Update root metagraph to new API

## [0.5.3] - 2024-09-18

### 🐛 Bug Fixes

- Fix #301: Fix incorrect type field on the table

## [0.5.2] - 2024-09-18

### 🐛 Bug Fixes

- Fix #301: Fix miner type of metagraph table on coldkey page
- Fix #263: Fix Mobile Version of Subnet Validator Emissions Per Miner

## [0.5.1] - 2024-09-18

### 🐛 Bug Fixes

- Fix #287: Fix Copy Confirmation for 'registered to' on Single Validator page(Mobile)

## [0.5.0] - 2024-09-18

### 🚀 Features

- Set correct instructions
- Add Time Interval Selector

### 🐛 Bug Fixes

- Fix mobile responsive of block & validator table on homepage

## [0.4.0] - 2024-09-17

### 🐛 Bug Fixes

- Tag push

## [0.3.0] - 2024-09-17

### 🚀 Features

- Add instructions


